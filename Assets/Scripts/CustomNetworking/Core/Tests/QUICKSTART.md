# NetworkDebugManager 测试快速开始指南

## 🚀 快速开始

### 1. 运行基础测试

#### 方法A: Unity Test Runner (推荐)
```
1. 打开 Unity Editor
2. 前往 Window > General > Test Runner
3. 选择 "EditMode" 标签页
4. 点击 "Run All" 按钮
5. 查看测试结果
```

#### 方法B: 手动测试运行器
```
1. 创建空场景
2. 添加空GameObject
3. 添加 TestRunner 组件
4. 在Inspector中点击 "Run All Tests"
5. 查看Console输出
```

### 2. 查看测试结果

#### Console输出示例
```
[PASS] BasicFunctionality.SingletonPattern: 单例模式工作正常
[PASS] UIControl.ShowDebugUI: 显示UI成功
[PASS] UIControl.HideDebugUI: 隐藏UI成功
[PASS] Logging.LogDebug: 日志记录成功
[TestRunner] 测试完成! 总计: 12, 通过: 12, 失败: 0
[TestRunner] 成功率: 100.0%
```

#### HTML报告
- 自动生成在 `Assets/TestReports/` 目录
- 包含详细的测试结果和时间戳
- 可在浏览器中查看

### 3. 运行集成测试

#### 启用PlayMode测试
```
1. 在Test Runner中选择 "PlayMode" 标签页
2. 点击 "Run All" 运行集成测试
3. 等待测试完成 (可能需要几分钟)
```

#### 集成测试内容
- 与NetworkManager的交互
- 与ErrorHandler的事件处理
- 与SynchronizationManager的统计更新
- 性能压力测试

## 🔧 常见问题解决

### 问题1: "Assembly not found" 错误
**解决方案:**
```
1. 检查 Tests.asmdef 文件
2. 确保引用了 CustomNetworking 程序集
3. 重新导入项目 (Assets > Reimport All)
```

### 问题2: 测试失败 "Instance is null"
**解决方案:**
```
1. 确保场景中没有其他NetworkDebugManager实例
2. 检查单例重置是否正确
3. 重启Unity Editor
```

### 问题3: 集成测试超时
**解决方案:**
```
1. 增加测试超时时间
2. 检查网络组件是否正确初始化
3. 查看Console是否有错误信息
```

## 📊 测试覆盖范围

### ✅ 已覆盖功能
- [x] 单例模式
- [x] UI显示/隐藏
- [x] 日志记录和清理
- [x] 性能统计获取
- [x] 网络诊断集成
- [x] 错误事件处理
- [x] 同步管理器集成

### 🔄 部分覆盖
- [~] UI绘制功能 (基础测试)
- [~] 性能监控 (基础验证)
- [~] 网络质量分析 (模拟数据)

### ❌ 未覆盖 (计划中)
- [ ] 实际网络环境测试
- [ ] 多客户端压力测试
- [ ] 内存泄漏检测
- [ ] UI自动化测试

## 🎯 下一步

### 立即可做
1. **运行所有测试** - 验证当前功能
2. **查看测试报告** - 了解详细结果
3. **修复失败测试** - 如果有的话

### 短期目标 (1-2周)
1. **添加更多单元测试** - 提高覆盖率
2. **优化集成测试** - 减少执行时间
3. **设置CI/CD** - 自动化测试流程

### 长期目标 (1-2月)
1. **性能基准测试** - 建立性能标准
2. **UI自动化测试** - 完整UI测试
3. **网络环境模拟** - 真实网络条件测试

## 📝 贡献测试

### 添加新测试
```csharp
[Test]
public void YourNewTest_Condition_ExpectedResult()
{
    // Arrange
    var debugManager = NetworkDebugManager.Instance;
    
    // Act
    debugManager.YourMethod();
    
    // Assert
    Assert.IsTrue(condition, "Error message");
}
```

### 测试命名规范
- **格式**: `MethodName_Condition_ExpectedResult`
- **示例**: `LogDebug_WithInfoType_ShouldAddEntry`

### 测试分类
- **Unit Tests**: 单个方法/功能测试
- **Integration Tests**: 组件间交互测试
- **Performance Tests**: 性能和压力测试

## 🔍 调试技巧

### 启用详细日志
```csharp
[SetUp]
public void SetUp()
{
    Debug.unityLogger.logEnabled = true;
    // 其他设置
}
```

### 使用断点调试
1. 在IDE中设置断点
2. 使用Unity Editor的调试模式
3. 逐步执行测试代码

### 检查测试隔离
```csharp
[TearDown]
public void TearDown()
{
    // 确保清理所有测试数据
    TestUtilities.ResetAllSingletons();
    // 销毁测试对象
}
```

## 📞 获取帮助

### 文档资源
- `README.md` - 完整测试文档
- `TestUtilities.cs` - 辅助方法参考
- Unity Test Framework 官方文档

### 常用命令
```bash
# 命令行运行测试
Unity -batchmode -runTests -testPlatform EditMode

# 生成测试报告
Unity -batchmode -runTests -testResults results.xml

# 运行特定测试
Unity -batchmode -runTests -testFilter "NetworkDebugManagerTests"
```

---

**开始测试吧！** 🎉

运行第一个测试只需要2分钟，让我们确保NetworkDebugManager工作正常！
