using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using CustomNetworking.Debug;
using CustomNetworking.Core;
using CustomNetworking.ErrorHandling;
using CustomNetworking.Synchronization;

namespace CustomNetworking.Tests
{
    /// <summary>
    /// NetworkDebugManager 集成测试
    /// 测试与其他网络组件的交互
    /// </summary>
    public class NetworkDebugManagerIntegrationTests
    {
        private GameObject debugManagerObject;
        private GameObject networkManagerObject;
        private GameObject syncManagerObject;
        private GameObject errorHandlerObject;
        
        private NetworkDebugManager debugManager;
        private MockNetworkManager mockNetworkManager;
        private NetworkSynchronizationManager syncManager;
        private NetworkErrorHandler errorHandler;

        [SetUp]
        public void SetUp()
        {
            // 创建调试管理器
            debugManagerObject = new GameObject("NetworkDebugManager");
            debugManager = debugManagerObject.AddComponent<NetworkDebugManager>();

            // 创建模拟网络管理器
            networkManagerObject = new GameObject("MockNetworkManager");
            mockNetworkManager = networkManagerObject.AddComponent<MockNetworkManager>();

            // 创建同步管理器
            syncManagerObject = new GameObject("NetworkSynchronizationManager");
            syncManager = syncManagerObject.AddComponent<NetworkSynchronizationManager>();

            // 创建错误处理器
            errorHandlerObject = new GameObject("NetworkErrorHandler");
            errorHandler = errorHandlerObject.AddComponent<NetworkErrorHandler>();
        }

        [TearDown]
        public void TearDown()
        {
            // 清理所有测试对象
            if (debugManagerObject != null) Object.DestroyImmediate(debugManagerObject);
            if (networkManagerObject != null) Object.DestroyImmediate(networkManagerObject);
            if (syncManagerObject != null) Object.DestroyImmediate(syncManagerObject);
            if (errorHandlerObject != null) Object.DestroyImmediate(errorHandlerObject);

            // 重置单例
            ResetSingletons();
        }

        #region 网络管理器集成测试

        [UnityTest]
        public IEnumerator Integration_WithNetworkManager_ShouldUpdateDiagnostics()
        {
            // Arrange
            mockNetworkManager.SetMockDiagnostics(new NetworkDiagnostics
            {
                State = ConnectionState.Connected,
                Quality = NetworkQuality.Good,
                Latency = 50f,
                PacketLoss = 0.01f,
                BandwidthUsage = 25f,
                Stability = 0.95f
            });

            // Act - 等待调试管理器更新
            yield return new WaitForSeconds(1f);

            // Assert
            var stats = debugManager.GetDebugStats();
            Assert.AreEqual(50f, stats.CurrentLatency, 0.1f);
            Assert.AreEqual(25f, stats.CurrentBandwidth, 0.1f);
            Assert.AreEqual(0.01f, stats.PacketLoss, 0.001f);
            Assert.AreEqual("Connected", stats.ConnectionState);
            Assert.AreEqual("Good", stats.NetworkQuality);
        }

        [Test]
        public void Integration_WithNullNetworkManager_ShouldHandleGracefully()
        {
            // Arrange
            Object.DestroyImmediate(networkManagerObject);

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var stats = debugManager.GetDebugStats();
                Assert.AreEqual(0f, stats.CurrentLatency);
                Assert.AreEqual(0f, stats.CurrentBandwidth);
            });
        }

        #endregion

        #region 同步管理器集成测试

        [UnityTest]
        public IEnumerator Integration_WithSyncManager_ShouldUpdateSyncStats()
        {
            // Arrange - 等待同步管理器初始化
            yield return new WaitForSeconds(0.5f);

            // Act
            var stats = debugManager.GetDebugStats();

            // Assert
            Assert.GreaterOrEqual(stats.SyncedObjects, 0);
            Assert.GreaterOrEqual(stats.SyncRate, 0);
        }

        #endregion

        #region 错误处理器集成测试

        [Test]
        public void Integration_WithErrorHandler_ShouldReceiveErrorEvents()
        {
            // Arrange
            var initialLogCount = GetLogEntryCount();

            // Act
            var testError = new NetworkError
            {
                Type = NetworkErrorType.ConnectionTimeout,
                Message = "Test connection timeout",
                Timestamp = System.DateTime.Now
            };
            
            errorHandler.TriggerError(testError);

            // Assert
            var newLogCount = GetLogEntryCount();
            Assert.Greater(newLogCount, initialLogCount);
        }

        [Test]
        public void Integration_WithErrorHandler_ShouldReceiveConnectionEvents()
        {
            // Arrange
            var initialLogCount = GetLogEntryCount();

            // Act
            errorHandler.TriggerConnectionLost();
            errorHandler.TriggerConnectionRestored();

            // Assert
            var newLogCount = GetLogEntryCount();
            Assert.AreEqual(initialLogCount + 2, newLogCount);
        }

        #endregion

        #region 多组件协同测试

        [UnityTest]
        public IEnumerator Integration_AllComponents_ShouldWorkTogether()
        {
            // Arrange
            mockNetworkManager.SetMockDiagnostics(new NetworkDiagnostics
            {
                State = ConnectionState.Connected,
                Quality = NetworkQuality.Excellent,
                Latency = 30f,
                PacketLoss = 0.005f,
                BandwidthUsage = 15f,
                Stability = 0.98f
            });

            // Act - 等待所有组件更新
            yield return new WaitForSeconds(1f);

            // 触发一些事件
            var testError = new NetworkError
            {
                Type = NetworkErrorType.DataCorruption,
                Message = "Test data corruption",
                Timestamp = System.DateTime.Now
            };
            errorHandler.TriggerError(testError);

            yield return new WaitForSeconds(0.1f);

            // Assert
            var stats = debugManager.GetDebugStats();
            
            // 验证网络诊断信息
            Assert.AreEqual(30f, stats.CurrentLatency, 0.1f);
            Assert.AreEqual("Connected", stats.ConnectionState);
            Assert.AreEqual("Excellent", stats.NetworkQuality);
            
            // 验证同步信息
            Assert.GreaterOrEqual(stats.SyncedObjects, 0);
            
            // 验证错误日志
            Assert.Greater(GetLogEntryCount(), 0);
        }

        #endregion

        #region 性能压力测试

        [UnityTest]
        public IEnumerator StressTest_HighFrequencyUpdates_ShouldMaintainPerformance()
        {
            // Arrange
            var startTime = Time.realtimeSinceStartup;
            
            // Act - 高频率更新网络诊断
            for (int i = 0; i < 100; i++)
            {
                mockNetworkManager.SetMockDiagnostics(new NetworkDiagnostics
                {
                    State = ConnectionState.Connected,
                    Quality = NetworkQuality.Good,
                    Latency = Random.Range(20f, 100f),
                    PacketLoss = Random.Range(0f, 0.05f),
                    BandwidthUsage = Random.Range(10f, 50f),
                    Stability = Random.Range(0.8f, 1f)
                });
                
                if (i % 10 == 0)
                {
                    yield return null; // 每10次更新让出一帧
                }
            }

            // Assert
            var endTime = Time.realtimeSinceStartup;
            var duration = endTime - startTime;
            
            // 性能测试：100次更新应该在合理时间内完成
            Assert.Less(duration, 5f, "High frequency updates took too long");
            
            // 验证最终状态仍然有效
            var stats = debugManager.GetDebugStats();
            Assert.GreaterOrEqual(stats.CurrentLatency, 0);
        }

        [UnityTest]
        public IEnumerator StressTest_ManyLogEntries_ShouldMaintainPerformance()
        {
            // Arrange
            var startTime = Time.realtimeSinceStartup;
            
            // Act - 大量日志记录
            for (int i = 0; i < 200; i++)
            {
                debugManager.LogDebug($"Stress test log entry {i}", DebugLogType.Info);
                
                if (i % 20 == 0)
                {
                    yield return null; // 每20次记录让出一帧
                }
            }

            // Assert
            var endTime = Time.realtimeSinceStartup;
            var duration = endTime - startTime;
            
            // 性能测试：200条日志应该在合理时间内完成
            Assert.Less(duration, 3f, "Many log entries took too long");
            
            // 验证日志数量被正确限制
            var logCount = GetLogEntryCount();
            var maxEntries = GetMaxLogEntries();
            Assert.LessOrEqual(logCount, maxEntries);
        }

        #endregion

        #region 辅助方法和模拟类

        /// <summary>
        /// 模拟网络管理器
        /// </summary>
        private class MockNetworkManager : MonoBehaviour, INetworkManager
        {
            private NetworkDiagnostics _mockDiagnostics;
            
            public bool IsConnected => true;
            public bool IsConnecting => false;

            public void SetMockDiagnostics(NetworkDiagnostics diagnostics)
            {
                _mockDiagnostics = diagnostics;
            }

            public System.Threading.Tasks.Task SendHeartbeat()
            {
                return System.Threading.Tasks.Task.CompletedTask;
            }

            public System.Threading.Tasks.Task ReconnectAsync()
            {
                return System.Threading.Tasks.Task.CompletedTask;
            }

            public void RequestResync() { }

            public System.Threading.Tasks.Task RequestFullResync()
            {
                return System.Threading.Tasks.Task.CompletedTask;
            }

            public NetworkDiagnostics GetNetworkDiagnostics()
            {
                return _mockDiagnostics;
            }
        }

        /// <summary>
        /// 获取日志条目数量
        /// </summary>
        private int GetLogEntryCount()
        {
            var field = typeof(NetworkDebugManager).GetField("_logEntries", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var logEntries = field.GetValue(debugManager) as System.Collections.IList;
            return logEntries?.Count ?? 0;
        }

        /// <summary>
        /// 获取最大日志条目数
        /// </summary>
        private int GetMaxLogEntries()
        {
            var field = typeof(NetworkDebugManager).GetField("maxLogEntries", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (int)field.GetValue(debugManager);
        }

        /// <summary>
        /// 重置所有单例
        /// </summary>
        private void ResetSingletons()
        {
            // 重置NetworkDebugManager单例
            var debugField = typeof(NetworkDebugManager).GetField("_instance", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            debugField?.SetValue(null, null);

            // 重置NetworkSynchronizationManager单例
            var syncField = typeof(NetworkSynchronizationManager).GetField("_instance", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            syncField?.SetValue(null, null);

            // 重置NetworkErrorHandler单例
            var errorField = typeof(NetworkErrorHandler).GetField("_instance", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            errorField?.SetValue(null, null);
        }

        #endregion
    }
}
